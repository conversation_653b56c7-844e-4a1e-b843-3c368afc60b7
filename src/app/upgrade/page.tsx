'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Check, Star, Heart, MessageCircle, Eye, Shield, Zap } from 'lucide-react';

interface PricingPlan {
  id: string;
  name: string;
  price: number;
  duration: number;
  features: string[];
  popular?: boolean;
  savings?: number;
}

const plans: PricingPlan[] = [
  {
    id: 'basic',
    name: 'Basic',
    price: 500,
    duration: 30,
    features: [
      'Unlimited profile views',
      'Basic matching algorithm',
      'Send 10 messages per day',
      'Standard customer support'
    ]
  },
  {
    id: 'premium',
    name: 'Premium',
    price: 1500,
    duration: 120,
    features: [
      'All Basic features',
      'Advanced matching algorithm',
      'Unlimited messages',
      'See who liked you',
      'Priority customer support',
      'Profile boost',
      'Advanced filters',
      'Read receipts'
    ],
    popular: true,
    savings: 25
  },
  {
    id: 'vip',
    name: 'VIP',
    price: 3000,
    duration: 120,
    features: [
      'All Premium features',
      'VIP badge on profile',
      'Exclusive VIP events',
      'Personal matchmaker',
      '24/7 priority support',
      'Profile verification',
      'Advanced analytics',
      'Custom profile themes'
    ],
    savings: 40
  }
];

export default function UpgradePage() {
  const [selectedPlan, setSelectedPlan] = useState<string>('premium');
  const [loading, setLoading] = useState(false);
  const [user, setUser] = useState<any>(null);
  const router = useRouter();

  useEffect(() => {
    fetchUser();
  }, []);

  const fetchUser = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        router.push('/login');
        return;
      }

      const response = await fetch('/api/user/profile', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setUser(data.user);
      }
    } catch (error) {
      console.error('Error fetching user:', error);
    }
  };

  const handleUpgrade = async () => {
    if (!user) return;

    setLoading(true);
    try {
      const token = localStorage.getItem('token');
      const plan = plans.find(p => p.id === selectedPlan);
      
      if (!plan) return;

      const response = await fetch('/api/payments/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          planId: plan.id,
          amount: plan.price,
          currency: 'KES',
          description: `${plan.name} Plan - ${plan.duration} days`
        })
      });

      if (response.ok) {
        const data = await response.json();
        
        // Redirect to payment page or handle payment flow
        if (data.paymentUrl) {
          window.location.href = data.paymentUrl;
        } else {
          // For M-Pesa, show payment instructions
          router.push(`/payment/instructions?paymentId=${data.paymentId}`);
        }
      }
    } catch (error) {
      console.error('Error creating payment:', error);
    } finally {
      setLoading(false);
    }
  };

  const selectedPlanData = plans.find(p => p.id === selectedPlan);

  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-50 to-purple-50 py-12">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              Upgrade Your Experience
            </h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Unlock premium features and find your perfect match faster with our exclusive plans
            </p>
          </div>

          {/* Current Status */}
          {user && (
            <Card className="mb-8">
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-semibold">Current Status</h3>
                    <p className="text-gray-600">
                      {user.isPaid ? (
                        <>
                          <Badge className="bg-green-100 text-green-800 mr-2">
                            Premium Active
                          </Badge>
                          Expires: {new Date(user.subscriptionExpiresAt).toLocaleDateString()}
                        </>
                      ) : (
                        <>
                          <Badge variant="secondary" className="mr-2">
                            Free Plan
                          </Badge>
                          Limited features available
                        </>
                      )}
                    </p>
                  </div>
                  {user.isPaid && (
                    <Button variant="outline" onClick={() => router.push('/matches')}>
                      Go to Matches
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Pricing Plans */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
            {plans.map((plan) => (
              <Card 
                key={plan.id} 
                className={`relative ${plan.popular ? 'ring-2 ring-primary shadow-lg' : ''}`}
              >
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-primary text-white px-4 py-1">
                      Most Popular
                    </Badge>
                  </div>
                )}
                
                <CardHeader className="text-center pb-4">
                  <CardTitle className="text-2xl font-bold">{plan.name}</CardTitle>
                  <div className="mt-4">
                    <span className="text-4xl font-bold">KES {plan.price.toLocaleString()}</span>
                    <span className="text-gray-500">/{plan.duration} days</span>
                  </div>
                  {plan.savings && (
                    <div className="mt-2">
                      <Badge variant="secondary" className="bg-green-100 text-green-800">
                        Save {plan.savings}%
                      </Badge>
                    </div>
                  )}
                </CardHeader>
                
                <CardContent>
                  <ul className="space-y-3 mb-6">
                    {plan.features.map((feature, index) => (
                      <li key={index} className="flex items-start">
                        <Check className="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                        <span className="text-gray-700">{feature}</span>
                      </li>
                    ))}
                  </ul>
                  
                  <Button 
                    className={`w-full ${plan.popular ? 'bg-primary hover:bg-primary/90' : 'bg-gray-800 hover:bg-gray-900'}`}
                    onClick={() => setSelectedPlan(plan.id)}
                    variant={plan.popular ? 'default' : 'secondary'}
                  >
                    {selectedPlan === plan.id ? 'Selected' : 'Choose Plan'}
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Selected Plan Summary */}
          {selectedPlanData && (
            <Card className="mb-8">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Star className="w-5 h-5 text-yellow-500" />
                  Selected Plan: {selectedPlanData.name}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-semibold mb-3">Plan Details</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>Duration:</span>
                        <span>{selectedPlanData.duration} days</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Price:</span>
                        <span className="font-semibold">KES {selectedPlanData.price.toLocaleString()}</span>
                      </div>
                      {selectedPlanData.savings && (
                        <div className="flex justify-between text-green-600">
                          <span>Savings:</span>
                          <span>{selectedPlanData.savings}%</span>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="font-semibold mb-3">Key Benefits</h4>
                    <div className="space-y-2 text-sm">
                      {selectedPlanData.features.slice(0, 4).map((feature, index) => (
                        <div key={index} className="flex items-center">
                          <Check className="w-4 h-4 text-green-500 mr-2" />
                          <span>{feature}</span>
                        </div>
                      ))}
                      {selectedPlanData.features.length > 4 && (
                        <div className="text-gray-500">
                          +{selectedPlanData.features.length - 4} more features
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Payment Methods */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle>Payment Methods</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="flex items-center p-4 border rounded-lg">
                  <div className="w-8 h-8 bg-green-500 rounded mr-3"></div>
                  <div>
                    <div className="font-semibold">M-Pesa</div>
                    <div className="text-sm text-gray-500">Mobile Money</div>
                  </div>
                </div>
                <div className="flex items-center p-4 border rounded-lg">
                  <div className="w-8 h-8 bg-blue-500 rounded mr-3"></div>
                  <div>
                    <div className="font-semibold">Card Payment</div>
                    <div className="text-sm text-gray-500">Credit/Debit Card</div>
                  </div>
                </div>
                <div className="flex items-center p-4 border rounded-lg">
                  <div className="w-8 h-8 bg-purple-500 rounded mr-3"></div>
                  <div>
                    <div className="font-semibold">Bank Transfer</div>
                    <div className="text-sm text-gray-500">Direct Transfer</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="text-center space-y-4">
            <Button 
              size="lg" 
              className="px-8 py-3 text-lg"
              onClick={handleUpgrade}
              disabled={loading}
            >
              {loading ? 'Processing...' : `Upgrade to ${selectedPlanData?.name} - KES ${selectedPlanData?.price.toLocaleString()}`}
            </Button>
            
            <div className="text-sm text-gray-500">
              Secure payment powered by Stripe • 30-day money-back guarantee
            </div>
          </div>

          {/* Trust Indicators */}
          <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
            <div className="flex flex-col items-center">
              <Shield className="w-8 h-8 text-green-500 mb-2" />
              <h4 className="font-semibold">Secure Payment</h4>
              <p className="text-sm text-gray-500">Bank-level security</p>
            </div>
            <div className="flex flex-col items-center">
              <Zap className="w-8 h-8 text-blue-500 mb-2" />
              <h4 className="font-semibold">Instant Access</h4>
              <p className="text-sm text-gray-500">Activate immediately</p>
            </div>
            <div className="flex flex-col items-center">
              <Heart className="w-8 h-8 text-red-500 mb-2" />
              <h4 className="font-semibold">Money Back</h4>
              <p className="text-sm text-gray-500">30-day guarantee</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 