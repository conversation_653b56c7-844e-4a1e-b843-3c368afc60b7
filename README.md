# KenyaMatch - Kenyan Dating Platform

KenyaMatch is a modern, scalable dating web application built specifically for the Kenyan market. It connects singles across Kenya, helping them find meaningful relationships through a secure and user-friendly platform.

## 🚀 Features

### Core Features
- **User Registration & Authentication**: Secure user registration with email verification
- **Detailed Profiles**: Comprehensive profiles including tribe, religion, occupation, and children information
- **Smart Matching**: Advanced matching algorithm based on location, preferences, and compatibility
- **Real-time Chat**: Instant messaging between matched users
- **Payment Integration**: M-Pesa and Stripe payment processing for premium features
- **Location-based Matching**: Find matches in your area using Kenyan towns and counties
- **Admin Panel**: Comprehensive admin interface for user and content management

### Technical Features
- **Mobile-First Design**: Responsive design optimized for mobile devices
- **Real-time Notifications**: Instant notifications for matches, messages, and payments
- **Image Upload**: Profile picture management with Cloudinary integration
- **Search & Filters**: Advanced search with multiple filtering options
- **Blocking & Reporting**: User safety features with blocking and reporting capabilities
- **Subscription Management**: 120-day premium access with payment processing

## 🛠️ Tech Stack

### Frontend
- **Next.js 15** - React framework with App Router
- **TypeScript** - Type-safe development
- **Tailwind CSS** - Utility-first CSS framework
- **Lucide React** - Beautiful icons
- **React Hook Form** - Form handling and validation
- **React Hot Toast** - Toast notifications
- **Framer Motion** - Animations

### Backend
- **Next.js API Routes** - Serverless API endpoints
- **Prisma** - Type-safe database ORM
- **PostgreSQL** - Primary database
- **PostGIS** - Geographic data support
- **JWT** - Authentication tokens
- **bcryptjs** - Password hashing

### External Services
- **Cloudinary** - Image upload and management
- **Stripe** - Payment processing
- **M-Pesa API** - Kenyan mobile money integration
- **Supabase** - Alternative auth and storage (optional)

## 📋 Prerequisites

Before running this project, make sure you have:

- **Node.js** (v18 or higher)
- **PostgreSQL** database
- **PostGIS** extension enabled
- **npm** or **yarn** package manager

## 🚀 Quick Start

### 1. Clone the Repository

```bash
git clone <repository-url>
cd kenyamatch
```

### 2. Install Dependencies

```bash
npm install
```

### 3. Environment Setup

Copy the environment example file and configure your variables:

```bash
cp env.example .env.local
```

Update the `.env.local` file with your configuration:

```env
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/kenyamatch"

# NextAuth
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-nextauth-secret-key-here"

# JWT
JWT_SECRET="your-jwt-secret-key-here"

# Stripe (for payments)
STRIPE_PUBLISHABLE_KEY="pk_test_your_stripe_publishable_key"
STRIPE_SECRET_KEY="sk_test_your_stripe_secret_key"
STRIPE_WEBHOOK_SECRET="whsec_your_stripe_webhook_secret"

# Cloudinary (for image uploads)
CLOUDINARY_CLOUD_NAME="your_cloudinary_cloud_name"
CLOUDINARY_API_KEY="your_cloudinary_api_key"
CLOUDINARY_API_SECRET="your_cloudinary_api_secret"

# M-Pesa (for Kenyan payments)
MPESA_CONSUMER_KEY="your_mpesa_consumer_key"
MPESA_CONSUMER_SECRET="your_mpesa_consumer_secret"
MPESA_PASSKEY="your_mpesa_passkey"
MPESA_BUSINESS_SHORTCODE="your_mpesa_business_shortcode"

# App Configuration
NEXT_PUBLIC_APP_URL="http://localhost:3000"
SUBSCRIPTION_PRICE=1200
SUBSCRIPTION_DURATION_DAYS=120
```

### 4. Database Setup

Generate Prisma client and run migrations:

```bash
# Generate Prisma client
npm run db:generate

# Push schema to database
npm run db:push

# Seed the database with sample data
npm run db:seed
```

### 5. Start Development Server

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to view the application.

## 📁 Project Structure

```
src/
├── app/                    # Next.js App Router
│   ├── (auth)/            # Authentication routes
│   │   ├── login/
│   │   ├── register/
│   │   └── verify/
│   ├── (protected)/       # Protected user routes
│   │   ├── matches/
│   │   ├── messages/
│   │   ├── profile/
│   │   └── settings/
│   ├── admin/             # Admin routes
│   │   ├── dashboard/
│   │   ├── users/
│   │   ├── reports/
│   │   └── payments/
│   ├── api/               # API routes
│   │   ├── auth/
│   │   ├── matches/
│   │   ├── messages/
│   │   ├── payments/
│   │   └── users/
│   └── page.tsx           # Landing page
├── components/            # Reusable components
│   ├── auth/
│   ├── layout/
│   ├── matches/
│   ├── messages/
│   └── ui/
├── lib/                   # Utility libraries
│   ├── prisma.ts
│   ├── auth.ts
│   ├── storage.ts
│   ├── payments.ts
│   └── utils.ts
└── types/                 # TypeScript type definitions
    └── index.ts
```

## 🗄️ Database Schema

The application uses the following main tables:

- **users** - User accounts and authentication
- **profiles** - Detailed user profiles
- **towns** - Kenyan towns and counties with coordinates
- **matches** - User matches and relationships
- **messages** - Chat messages between matched users
- **payments** - Payment records and subscriptions
- **preferences** - User matching preferences
- **children** - Children information
- **reports** - User reports and safety
- **blocked_users** - User blocking functionality
- **notifications** - System notifications

## 🔐 Authentication & Security

- **JWT-based authentication** with secure token handling
- **Password hashing** using bcryptjs
- **Input validation** with Zod schemas
- **CORS protection** and rate limiting
- **SQL injection prevention** through Prisma ORM
- **XSS protection** with proper input sanitization

## 💳 Payment Integration

### Stripe Integration
- Credit/debit card payments
- Webhook handling for payment status updates
- Secure payment processing

### M-Pesa Integration
- Kenyan mobile money payments
- STK Push for payment initiation
- Callback handling for payment confirmation

## 📱 Mobile Responsiveness

The application is built with a mobile-first approach:
- Responsive design for all screen sizes
- Touch-friendly interface
- Optimized for mobile performance
- Progressive Web App (PWA) ready

## 🚀 Deployment

### Vercel Deployment

1. Connect your repository to Vercel
2. Configure environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

### Docker Deployment

```bash
# Build the Docker image
docker build -t kenyamatch .

# Run the container
docker run -p 3000:3000 kenyamatch
```

## 🧪 Testing

```bash
# Run tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

## 📊 API Documentation

### Authentication Endpoints

- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout

### User Endpoints

- `GET /api/users/profile` - Get user profile
- `PUT /api/users/profile` - Update user profile
- `GET /api/users/preferences` - Get user preferences
- `PUT /api/users/preferences` - Update user preferences

### Matching Endpoints

- `GET /api/matches` - Get potential matches
- `POST /api/matches` - Create a match
- `GET /api/matches/conversations` - Get user conversations

### Messaging Endpoints

- `GET /api/messages` - Get messages for a match
- `POST /api/messages` - Send a message

### Payment Endpoints

- `POST /api/payments` - Initiate payment
- `GET /api/payments` - Get payment history
- `POST /api/payments/webhook` - Payment webhook handler

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:

- Email: <EMAIL>
- Documentation: [docs.kenyamatch.com](https://docs.kenyamatch.com)
- Issues: [GitHub Issues](https://github.com/your-repo/issues)

## 🙏 Acknowledgments

- Next.js team for the amazing framework
- Prisma team for the excellent ORM
- Tailwind CSS for the utility-first CSS framework
- All contributors and beta testers

---

**Made with ❤️ for the Kenyan dating community**
