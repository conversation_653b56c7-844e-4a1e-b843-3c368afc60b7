import { NextRequest, NextResponse } from 'next/server'
import { verifyJWT, hashPassword } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { sendEmail } from '@/lib/email'

interface RouteParams {
  params: {
    id: string
    action: string
  }
}

export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    // Verify admin authentication
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const token = authHeader.substring(7)
    const payload = verifyJWT(token)
    if (!payload?.userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user is admin
    const adminUser = await prisma.user.findUnique({
      where: { id: payload.userId }
    })

    if (!adminUser || adminUser.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    const { id, action } = params

    // Validate user exists
    const user = await prisma.user.findUnique({
      where: { id },
      select: {
        id: true,
        email: true,
        name: true,
        isActive: true,
        isPaid: true,
        subscriptionExpiresAt: true
      }
    })

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Handle different actions
    switch (action) {
      case 'activate':
        if (user.isActive) {
          return NextResponse.json({ error: 'User is already active' }, { status: 400 })
        }
        
        await prisma.user.update({
          where: { id },
          data: { isActive: true }
        })

        // Send reactivation email
        try {
          await sendEmail({
            to: user.email,
            subject: 'Your KenyaMatch Account Has Been Reactivated',
            html: `
              <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h2 style="color: #ec4899;">Account Reactivated</h2>
                <p>Hello ${user.name || 'there'},</p>
                <p>Your KenyaMatch account has been reactivated by our admin team.</p>
                <p>You can now log in and continue using our platform to find meaningful connections.</p>
                <p>If you have any questions, please contact our support team.</p>
                <p>Best regards,<br>The KenyaMatch Team</p>
              </div>
            `
          })
        } catch (emailError) {
          console.error('Failed to send reactivation email:', emailError)
        }
        
        break

      case 'deactivate':
        if (!user.isActive) {
          return NextResponse.json({ error: 'User is already inactive' }, { status: 400 })
        }
        
        await prisma.user.update({
          where: { id },
          data: { isActive: false }
        })

        // Send deactivation email
        try {
          await sendEmail({
            to: user.email,
            subject: 'Your KenyaMatch Account Has Been Deactivated',
            html: `
              <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h2 style="color: #ec4899;">Account Deactivated</h2>
                <p>Hello ${user.name || 'there'},</p>
                <p>Your KenyaMatch account has been temporarily deactivated by our admin team.</p>
                <p>This may be due to a violation of our community guidelines or terms of service.</p>
                <p>If you believe this is an error or would like to appeal this decision, please contact our support team.</p>
                <p>Best regards,<br>The KenyaMatch Team</p>
              </div>
            `
          })
        } catch (emailError) {
          console.error('Failed to send deactivation email:', emailError)
        }
        
        break

      case 'send-email':
        // Send a general admin message
        try {
          await sendEmail({
            to: user.email,
            subject: 'Message from KenyaMatch Admin Team',
            html: `
              <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h2 style="color: #ec4899;">Message from Admin Team</h2>
                <p>Hello ${user.name || 'there'},</p>
                <p>This is a message from the KenyaMatch admin team.</p>
                <p>We're reaching out to ensure you're having a great experience on our platform.</p>
                <p>If you have any questions or need assistance, please don't hesitate to contact us.</p>
                <p>Thank you for being part of the KenyaMatch community!</p>
                <p>Best regards,<br>The KenyaMatch Team</p>
              </div>
            `
          })
        } catch (emailError) {
          console.error('Failed to send admin email:', emailError)
          return NextResponse.json({ error: 'Failed to send email' }, { status: 500 })
        }
        
        break

      case 'mark-paid':
        if (user.isPaid) {
          return NextResponse.json({ error: 'User is already marked as paid' }, { status: 400 })
        }

        // Set subscription to expire in 120 days (standard subscription period)
        const subscriptionExpiresAt = new Date()
        subscriptionExpiresAt.setDate(subscriptionExpiresAt.getDate() + 120)

        await prisma.user.update({
          where: { id },
          data: {
            isPaid: true,
            subscriptionExpiresAt: subscriptionExpiresAt
          }
        })

        // Create a payment record for admin tracking
        await prisma.payment.create({
          data: {
            userId: id,
            amount: 499, // Standard KES 499 price
            method: 'OTHER', // Admin override
            status: 'COMPLETED',
            transactionId: `ADMIN_${Date.now()}`,
            metadata: {
              adminOverride: true,
              adminUserId: payload.userId,
              reason: 'Admin marked as paid'
            }
          }
        })

        // Send confirmation email
        try {
          await sendEmail({
            to: user.email,
            subject: 'Your KenyaMatch Premium Access Has Been Activated',
            html: `
              <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h2 style="color: #ec4899;">Premium Access Activated!</h2>
                <p>Hello ${user.name || 'there'},</p>
                <p>Great news! Your KenyaMatch premium access has been activated by our admin team.</p>
                <p>You now have full access to:</p>
                <ul>
                  <li>View and connect with all matches</li>
                  <li>Send unlimited messages</li>
                  <li>Access premium features</li>
                </ul>
                <p>Your subscription is valid until ${subscriptionExpiresAt.toLocaleDateString()}.</p>
                <p>Start exploring and find your perfect match!</p>
                <p>Best regards,<br>The KenyaMatch Team</p>
              </div>
            `
          })
        } catch (emailError) {
          console.error('Failed to send premium activation email:', emailError)
        }

        break

      case 'mark-unpaid':
        if (!user.isPaid) {
          return NextResponse.json({ error: 'User is already marked as unpaid' }, { status: 400 })
        }

        await prisma.user.update({
          where: { id },
          data: {
            isPaid: false,
            subscriptionExpiresAt: null
          }
        })

        // Send notification email
        try {
          await sendEmail({
            to: user.email,
            subject: 'Your KenyaMatch Premium Access Has Expired',
            html: `
              <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h2 style="color: #ec4899;">Premium Access Update</h2>
                <p>Hello ${user.name || 'there'},</p>
                <p>Your KenyaMatch premium access has been updated by our admin team.</p>
                <p>To continue enjoying full access to our platform, please renew your subscription.</p>
                <p>You can still browse profiles, but connecting and messaging will require an active subscription.</p>
                <p>If you have any questions, please contact our support team.</p>
                <p>Best regards,<br>The KenyaMatch Team</p>
              </div>
            `
          })
        } catch (emailError) {
          console.error('Failed to send subscription update email:', emailError)
        }

        break

      case 'extend-subscription':
        const body = await request.json()
        const { days = 30 } = body // Default to 30 days extension

        const currentExpiry = user.subscriptionExpiresAt || new Date()
        const newExpiry = new Date(currentExpiry)
        newExpiry.setDate(newExpiry.getDate() + days)

        await prisma.user.update({
          where: { id },
          data: {
            isPaid: true,
            subscriptionExpiresAt: newExpiry
          }
        })

        // Send extension confirmation email
        try {
          await sendEmail({
            to: user.email,
            subject: 'Your KenyaMatch Subscription Has Been Extended',
            html: `
              <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h2 style="color: #ec4899;">Subscription Extended!</h2>
                <p>Hello ${user.name || 'there'},</p>
                <p>Great news! Your KenyaMatch subscription has been extended by our admin team.</p>
                <p>Your subscription is now valid until ${newExpiry.toLocaleDateString()}.</p>
                <p>Continue enjoying full access to all premium features!</p>
                <p>Best regards,<br>The KenyaMatch Team</p>
              </div>
            `
          })
        } catch (emailError) {
          console.error('Failed to send extension email:', emailError)
        }

        break

      case 'set-password':
        const passwordBody = await request.json()
        const { password } = passwordBody

        if (!password || password.length < 6) {
          return NextResponse.json({ error: 'Password must be at least 6 characters long' }, { status: 400 })
        }

        // Hash the new password
        const hashedPassword = await hashPassword(password)

        await prisma.user.update({
          where: { id },
          data: {
            passwordHash: hashedPassword
          }
        })

        // Send password update notification email
        try {
          await sendEmail({
            to: user.email,
            subject: 'Your KenyaMatch Password Has Been Updated',
            html: `
              <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h2 style="color: #ec4899;">Password Updated</h2>
                <p>Hello ${user.name || 'there'},</p>
                <p>Your KenyaMatch account password has been updated by our admin team.</p>
                <p>You can now log in with your new password.</p>
                <p>If you did not request this change, please contact our support team immediately.</p>
                <p>Best regards,<br>The KenyaMatch Team</p>
              </div>
            `
          })
        } catch (emailError) {
          console.error('Failed to send password update email:', emailError)
        }

        break

      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 })
    }

    return NextResponse.json({
      success: true,
      message: `User ${action} completed successfully`
    })

  } catch (error) {
    console.error('User action error:', error)
    return NextResponse.json(
      { error: 'Failed to perform user action' },
      { status: 500 }
    )
  }
}
