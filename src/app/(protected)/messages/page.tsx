'use client'

import { useEffect, useState, useRef } from 'react'
import toast from 'react-hot-toast'
import Image from 'next/image'
import { User2, MessageCircle } from 'lucide-react'

interface Conversation {
  matchId: string
  partner: {
    id: string
    name: string
    profilePicture?: string
    town?: string
    isOnline: boolean
  }
  lastMessage?: {
    content: string
    senderId: string
    createdAt: string
    isRead: boolean
  }
  messageCount: number
  matchedAt: string
}

interface Message {
  id: string
  senderId: string
  content: string
  createdAt: string
  sender: { profile: { fullName: string; profilePictures: { url: string }[] } }
}

export default function MessagesPage() {
  const [conversations, setConversations] = useState<Conversation[]>([])
  const [selectedConversation, setSelectedConversation] = useState<Conversation | null>(null)
  const [messages, setMessages] = useState<Message[]>([])
  const [newMessage, setNewMessage] = useState('')
  const [loadingConversations, setLoadingConversations] = useState(true)
  const [loadingMessages, setLoadingMessages] = useState(false)
  const [error, setError] = useState('')
  const messagesEndRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const fetchConversations = async () => {
      setLoadingConversations(true)
      setError('')
      try {
        const token = localStorage.getItem('kenyamatch_token')
        const res = await fetch('/api/matches/conversations', {
          headers: { Authorization: `Bearer ${token}` }
        })
        const data = await res.json()
        if (data.success) {
          setConversations(data.data)
        } else {
          setError(data.error || 'Failed to load conversations')
        }
      } catch (err) {
        setError('Failed to load conversations')
      } finally {
        setLoadingConversations(false)
      }
    }
    fetchConversations()
  }, [])

  useEffect(() => {
    if (!selectedConversation) return
    let polling: NodeJS.Timeout
    const fetchMessages = async () => {
      setLoadingMessages(true)
      setError('')
      try {
        const token = localStorage.getItem('kenyamatch_token')
        const res = await fetch(`/api/messages?matchId=${selectedConversation.matchId}`, {
          headers: { Authorization: `Bearer ${token}` }
        })
        const data = await res.json()
        if (data.success) {
          setMessages(data.data)
        } else {
          setError(data.error || 'Failed to load messages')
        }
      } catch (err) {
        setError('Failed to load messages')
      } finally {
        setLoadingMessages(false)
      }
    }
    fetchMessages()
    polling = setInterval(fetchMessages, 3000)
    return () => clearInterval(polling)
  }, [selectedConversation])

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  const handleSend = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!newMessage.trim() || !selectedConversation) return
    try {
      const token = localStorage.getItem('kenyamatch_token')
      const res = await fetch('/api/messages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`
        },
        body: JSON.stringify({ matchId: selectedConversation.matchId, content: newMessage })
      })
      const data = await res.json()
      if (data.success) {
        setNewMessage('')
        setMessages((prev) => [...prev, data.data])
      } else {
        toast.error(data.error || 'Could not send message')
      }
    } catch (err) {
      toast.error('Could not send message')
    }
  }

  return (
    <div className="flex h-[80vh] bg-white rounded-xl shadow overflow-hidden border">
      {/* Sidebar: Conversations */}
      <div className="w-72 border-r bg-gray-50 p-4 overflow-y-auto flex flex-col">
        <h2 className="font-bold mb-4 text-gray-700 text-lg flex items-center gap-2"><MessageCircle className="w-5 h-5 text-pink-500" /> Conversations</h2>
        {loadingConversations ? (
          <div className="text-gray-400">Loading...</div>
        ) : error ? (
          <div className="text-red-500">{error}</div>
        ) : conversations.length === 0 ? (
          <div className="text-gray-400">No conversations yet.</div>
        ) : (
          <ul className="space-y-1">
            {conversations.map((conversation) => {
              // Safety check to ensure conversation and partner exist
              if (!conversation || !conversation.partner) {
                return null
              }

              return (
                <li
                  key={conversation.matchId}
                  className={`flex items-center gap-3 p-2 rounded-lg cursor-pointer transition-colors mb-1 select-none ${selectedConversation?.matchId === conversation.matchId ? 'bg-pink-100 border border-pink-300' : 'hover:bg-gray-100'}`}
                  onClick={() => setSelectedConversation(conversation)}
                >
                  {conversation.partner.profilePicture ? (
                    <Image
                      src={conversation.partner.profilePicture}
                      alt={conversation.partner.name || 'User'}
                      width={40}
                      height={40}
                      className="rounded-full object-cover border"
                    />
                  ) : (
                    <span className="w-10 h-10 rounded-full bg-pink-200 flex items-center justify-center text-lg font-bold text-pink-700 border">
                      {conversation.partner.name?.[0] || '?'}
                    </span>
                  )}
                  <div className="flex-1 min-w-0">
                    <div className="font-medium text-gray-800 truncate">{conversation.partner.name || 'Unknown User'}</div>
                    {conversation.lastMessage && (
                      <div className="text-xs text-gray-500 truncate">
                        {conversation.lastMessage.content}
                      </div>
                    )}
                  </div>
                </li>
              )
            })}
          </ul>
        )}
      </div>
      {/* Chat Area */}
      <div className="flex-1 flex flex-col bg-gradient-to-br from-pink-50 to-white">
        {selectedConversation && selectedConversation.partner ? (
          <>
            {/* Chat Header */}
            <div className="border-b px-6 py-4 bg-white flex items-center gap-3 shadow-sm">
              {selectedConversation.partner.profilePicture ? (
                <Image
                  src={selectedConversation.partner.profilePicture}
                  alt={selectedConversation.partner.name || 'User'}
                  width={40}
                  height={40}
                  className="rounded-full object-cover border"
                />
              ) : (
                <span className="w-10 h-10 rounded-full bg-pink-200 flex items-center justify-center text-lg font-bold text-pink-700 border">
                  {selectedConversation.partner.name?.[0] || '?'}
                </span>
              )}
              <div className="flex-1">
                <span className="font-semibold text-gray-800 text-lg">
                  {selectedConversation.partner.name || 'Unknown User'}
                </span>
                {selectedConversation.partner.town && (
                  <div className="text-sm text-gray-500">{selectedConversation.partner.town}</div>
                )}
              </div>
              {selectedConversation.partner.isOnline && (
                <div className="flex items-center gap-1 text-green-600 text-sm">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  Online
                </div>
              )}
            </div>
            {/* Messages */}
            <div className="flex-1 overflow-y-auto p-6 space-y-4 bg-transparent">
              {loadingMessages ? (
                <div className="text-gray-400">Loading messages...</div>
              ) : error ? (
                <div className="text-red-500">{error}</div>
              ) : messages.length === 0 ? (
                <div className="text-gray-400">No messages yet.</div>
              ) : (
                messages.map((msg) => (
                  <div key={msg.id} className={`flex ${msg.senderId === localStorage.getItem('kenyamatch_userId') ? 'justify-end' : 'justify-start'}`}>
                    <div className={`max-w-xs px-4 py-2 rounded-2xl shadow-sm ${msg.senderId === localStorage.getItem('kenyamatch_userId') ? 'bg-pink-500 text-white rounded-br-none' : 'bg-white text-gray-900 border rounded-bl-none'}`}>
                      <div className="text-base leading-snug">{msg.content}</div>
                      <div className="text-xs mt-1 opacity-60 text-right">{new Date(msg.createdAt).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}</div>
                    </div>
                  </div>
                ))
              )}
              <div ref={messagesEndRef} />
            </div>
            {/* Message Input */}
            <form onSubmit={handleSend} className="p-4 border-t bg-white flex gap-2 items-center">
              <input
                type="text"
                value={newMessage}
                onChange={e => setNewMessage(e.target.value)}
                className="flex-1 px-4 py-2 rounded-full border border-gray-300 focus:outline-none focus:ring-2 focus:ring-pink-400 focus:border-pink-400 bg-gray-50"
                placeholder="Type your message..."
              />
              <button
                type="submit"
                className="bg-pink-500 hover:bg-pink-600 text-white px-6 py-2 rounded-full font-semibold shadow"
                disabled={!newMessage.trim()}
              >
                Send
              </button>
            </form>
          </>
        ) : (
          <div className="flex-1 flex flex-col items-center justify-center text-gray-400">
            <User2 className="w-16 h-16 mb-4 text-pink-200" />
            <div className="text-lg font-semibold">Select a conversation to start chatting.</div>
          </div>
        )}
      </div>
    </div>
  )
} 