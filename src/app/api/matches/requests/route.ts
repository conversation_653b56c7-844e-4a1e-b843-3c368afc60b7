import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { verifyJWT } from '@/lib/auth'
import { sendEmail } from '@/lib/email'

// GET - Fetch incoming match requests
export async function GET(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const token = authHeader.substring(7)
    const decoded = verifyJWT(token)
    if (!decoded) {
      return NextResponse.json(
        { success: false, error: 'Invalid token' },
        { status: 401 }
      )
    }

    // Get pending match requests where current user is user2 (receiver)
    const incomingRequests = await prisma.match.findMany({
      where: {
        user2Id: decoded.userId,
        status: 'pending'
      },
      include: {
        user1: {
          include: {
            profile: {
              include: {
                town: true,
                profilePictures: {
                  where: { isPrimary: true },
                  take: 1
                }
              }
            }
          }
        }
      },
      orderBy: { matchedAt: 'desc' }
    })

    // Get outgoing match requests where current user is user1 (sender)
    const outgoingRequests = await prisma.match.findMany({
      where: {
        user1Id: decoded.userId,
        status: 'pending'
      },
      include: {
        user2: {
          include: {
            profile: {
              include: {
                town: true,
                profilePictures: {
                  where: { isPrimary: true },
                  take: 1
                }
              }
            }
          }
        }
      },
      orderBy: { matchedAt: 'desc' }
    })

    return NextResponse.json({
      success: true,
      data: {
        incoming: incomingRequests,
        outgoing: outgoingRequests
      }
    })

  } catch (error) {
    console.error('Get match requests error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST - Respond to a match request (accept/reject)
export async function POST(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const token = authHeader.substring(7)
    const decoded = verifyJWT(token)
    if (!decoded) {
      return NextResponse.json(
        { success: false, error: 'Invalid token' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { matchId, action } = body // action: 'accept' or 'reject'

    if (!matchId || !action || !['accept', 'reject'].includes(action)) {
      return NextResponse.json(
        { success: false, error: 'Match ID and valid action (accept/reject) are required' },
        { status: 400 }
      )
    }

    // Find the match and verify user is the receiver
    const match = await prisma.match.findFirst({
      where: {
        id: matchId,
        user2Id: decoded.userId,
        status: 'pending'
      },
      include: {
        user1: {
          include: {
            profile: true
          }
        },
        user2: {
          include: {
            profile: true
          }
        }
      }
    })

    if (!match) {
      return NextResponse.json(
        { success: false, error: 'Match request not found or already responded to' },
        { status: 404 }
      )
    }

    // Update match status
    const newStatus = action === 'accept' ? 'accepted' : 'rejected'
    const updatedMatch = await prisma.match.update({
      where: { id: matchId },
      data: { status: newStatus }
    })

    // Create notification for the sender
    const notificationTitle = action === 'accept' ? 'Match Accepted!' : 'Match Request Response'
    const notificationMessage = action === 'accept' 
      ? `${match.user2.profile?.fullName || 'Someone'} accepted your match request! You can now start chatting.`
      : `${match.user2.profile?.fullName || 'Someone'} declined your match request.`

    await prisma.notification.create({
      data: {
        userId: match.user1Id,
        type: 'match',
        title: notificationTitle,
        message: notificationMessage,
        metadata: {
          matchId: match.id,
          action: action,
          responderId: decoded.userId
        }
      }
    })

    // Send email notification if match was accepted
    if (action === 'accept') {
      try {
        await sendEmail({
          to: match.user1.email,
          subject: 'Great News! Your Match Request Was Accepted',
          html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h2 style="color: #ec4899;">🎉 Match Accepted!</h2>
              <p>Hello ${match.user1.profile?.fullName || 'there'},</p>
              <p>Great news! <strong>${match.user2.profile?.fullName || 'Someone'}</strong> has accepted your match request!</p>
              <p>This could be the start of something special. Don't wait too long to start a conversation!</p>
              <p>
                <a href="${process.env.NEXT_PUBLIC_APP_URL}/messages" 
                   style="background-color: #ec4899; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
                  Start Chatting Now
                </a>
              </p>
              <p>Remember to be respectful and authentic in your conversations. Good luck!</p>
              <p>Best regards,<br>The KenyaMatch Team</p>
            </div>
          `
        })
      } catch (emailError) {
        console.error('Failed to send match acceptance email:', emailError)
      }
    }

    return NextResponse.json({
      success: true,
      data: updatedMatch,
      message: `Match request ${action}ed successfully`
    })

  } catch (error) {
    console.error('Respond to match request error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
