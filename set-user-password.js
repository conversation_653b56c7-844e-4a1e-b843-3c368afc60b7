// <PERSON>ript to set password for <PERSON> user
import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function setUserPassword() {
  try {
    // Find <PERSON> by email
    const user = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
      include: {
        profile: true
      }
    })

    if (!user) {
      console.log('User <EMAIL> not found')
      return
    }

    console.log('Found user:', {
      id: user.id,
      email: user.email,
      name: user.profile?.fullName || user.name
    })

    // Hash the password "jack75522r"
    const password = 'jack75522r'
    const hashedPassword = await bcrypt.hash(password, 12)

    // Update the user's password
    await prisma.user.update({
      where: { id: user.id },
      data: { passwordHash: hashedPassword }
    })

    console.log(`Password set successfully for ${user.email}`)
    console.log(`Password: ${password}`)
    
  } catch (error) {
    console.error('Error setting password:', error)
  } finally {
    await prisma.$disconnect()
  }
}

setUserPassword()
