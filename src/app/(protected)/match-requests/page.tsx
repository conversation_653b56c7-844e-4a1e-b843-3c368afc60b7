'use client'

import { useEffect, useState } from 'react'
import toast from 'react-hot-toast'
import Image from 'next/image'
import { Heart, X, Clock, Send, MapPin, Calendar, Users } from 'lucide-react'
import { calculateAge } from '@/lib/utils'

interface MatchRequest {
  id: string
  matchedAt: string
  user1: {
    id: string
    profile: {
      fullName: string
      dateOfBirth: string
      town: { name: string }
      tribe?: string
      profilePictures: { url: string }[]
    }
  }
  user2: {
    id: string
    profile: {
      fullName: string
      dateOfBirth: string
      town: { name: string }
      tribe?: string
      profilePictures: { url: string }[]
    }
  }
}

interface MatchRequestsData {
  incoming: MatchRequest[]
  outgoing: MatchRequest[]
}

export default function MatchRequestsPage() {
  const [requests, setRequests] = useState<MatchRequestsData>({ incoming: [], outgoing: [] })
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [activeTab, setActiveTab] = useState<'incoming' | 'outgoing'>('incoming')
  const [respondingTo, setRespondingTo] = useState<string | null>(null)

  useEffect(() => {
    fetchMatchRequests()
  }, [])

  const fetchMatchRequests = async () => {
    setLoading(true)
    setError('')
    try {
      const token = localStorage.getItem('kenyamatch_token')
      const res = await fetch('/api/matches/requests', {
        headers: { Authorization: `Bearer ${token}` }
      })
      const data = await res.json()
      if (data.success) {
        setRequests(data.data)
      } else {
        setError(data.error || 'Failed to load match requests')
      }
    } catch (err) {
      setError('Failed to load match requests')
    } finally {
      setLoading(false)
    }
  }

  const handleResponse = async (matchId: string, action: 'accept' | 'reject') => {
    setRespondingTo(matchId)
    try {
      const token = localStorage.getItem('kenyamatch_token')
      const res = await fetch('/api/matches/requests', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`
        },
        body: JSON.stringify({ matchId, action })
      })
      const data = await res.json()
      if (data.success) {
        toast.success(action === 'accept' ? 'Match accepted! 🎉' : 'Match request declined')
        fetchMatchRequests() // Refresh the list
      } else {
        toast.error(data.error || `Failed to ${action} match request`)
      }
    } catch (err) {
      toast.error(`Failed to ${action} match request`)
    } finally {
      setRespondingTo(null)
    }
  }

  if (loading) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-pink-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading match requests...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="text-center py-12">
          <div className="text-red-500 mb-4">{error}</div>
          <button
            onClick={fetchMatchRequests}
            className="bg-pink-500 hover:bg-pink-600 text-white px-6 py-2 rounded-lg"
          >
            Try Again
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Match Requests</h1>
        <p className="text-gray-600">Manage your incoming and outgoing match requests</p>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg mb-6">
        <button
          onClick={() => setActiveTab('incoming')}
          className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'incoming'
              ? 'bg-white text-pink-600 shadow-sm'
              : 'text-gray-500 hover:text-gray-700'
          }`}
        >
          Incoming ({requests.incoming.length})
        </button>
        <button
          onClick={() => setActiveTab('outgoing')}
          className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'outgoing'
              ? 'bg-white text-pink-600 shadow-sm'
              : 'text-gray-500 hover:text-gray-700'
          }`}
        >
          Sent ({requests.outgoing.length})
        </button>
      </div>

      {/* Content */}
      <div className="space-y-4">
        {activeTab === 'incoming' ? (
          requests.incoming.length === 0 ? (
            <div className="text-center py-12">
              <Heart className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No incoming requests</h3>
              <p className="text-gray-500">When someone sends you a match request, it will appear here.</p>
            </div>
          ) : (
            requests.incoming.map((request) => {
              const sender = request.user1
              const age = calculateAge(sender.profile.dateOfBirth)
              
              return (
                <div key={request.id} className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
                  <div className="flex items-start gap-4">
                    {/* Profile Picture */}
                    <div className="w-20 h-20 rounded-full overflow-hidden bg-gray-200 flex-shrink-0">
                      {sender.profile.profilePictures[0]?.url ? (
                        <Image
                          src={sender.profile.profilePictures[0].url}
                          alt={sender.profile.fullName}
                          width={80}
                          height={80}
                          className="object-cover w-full h-full"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center text-2xl font-bold text-gray-500">
                          {sender.profile.fullName.charAt(0)}
                        </div>
                      )}
                    </div>

                    {/* Profile Info */}
                    <div className="flex-1">
                      <h3 className="text-xl font-semibold text-gray-900 mb-1">
                        {sender.profile.fullName}
                      </h3>
                      
                      <div className="flex items-center gap-4 text-sm text-gray-600 mb-3">
                        <div className="flex items-center gap-1">
                          <Users className="w-4 h-4" />
                          <span>{age} years old</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <MapPin className="w-4 h-4" />
                          <span>{sender.profile.town.name}</span>
                        </div>
                        {sender.profile.tribe && (
                          <div className="flex items-center gap-1">
                            <span>{sender.profile.tribe}</span>
                          </div>
                        )}
                      </div>

                      <div className="flex items-center gap-1 text-xs text-gray-500 mb-4">
                        <Clock className="w-3 h-3" />
                        <span>Sent {new Date(request.matchedAt).toLocaleDateString()}</span>
                      </div>

                      {/* Action Buttons */}
                      <div className="flex gap-3">
                        <button
                          onClick={() => handleResponse(request.id, 'accept')}
                          disabled={respondingTo === request.id}
                          className="flex items-center gap-2 bg-pink-500 hover:bg-pink-600 text-white px-6 py-2 rounded-lg font-medium transition-colors disabled:opacity-50"
                        >
                          <Heart className="w-4 h-4" />
                          {respondingTo === request.id ? 'Accepting...' : 'Accept'}
                        </button>
                        <button
                          onClick={() => handleResponse(request.id, 'reject')}
                          disabled={respondingTo === request.id}
                          className="flex items-center gap-2 bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg font-medium transition-colors disabled:opacity-50"
                        >
                          <X className="w-4 h-4" />
                          {respondingTo === request.id ? 'Declining...' : 'Decline'}
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              )
            })
          )
        ) : (
          // Outgoing requests
          requests.outgoing.length === 0 ? (
            <div className="text-center py-12">
              <Send className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No sent requests</h3>
              <p className="text-gray-500">Match requests you send will appear here while pending.</p>
            </div>
          ) : (
            requests.outgoing.map((request) => {
              const receiver = request.user2
              const age = calculateAge(receiver.profile.dateOfBirth)
              
              return (
                <div key={request.id} className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
                  <div className="flex items-start gap-4">
                    {/* Profile Picture */}
                    <div className="w-20 h-20 rounded-full overflow-hidden bg-gray-200 flex-shrink-0">
                      {receiver.profile.profilePictures[0]?.url ? (
                        <Image
                          src={receiver.profile.profilePictures[0].url}
                          alt={receiver.profile.fullName}
                          width={80}
                          height={80}
                          className="object-cover w-full h-full"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center text-2xl font-bold text-gray-500">
                          {receiver.profile.fullName.charAt(0)}
                        </div>
                      )}
                    </div>

                    {/* Profile Info */}
                    <div className="flex-1">
                      <h3 className="text-xl font-semibold text-gray-900 mb-1">
                        {receiver.profile.fullName}
                      </h3>
                      
                      <div className="flex items-center gap-4 text-sm text-gray-600 mb-3">
                        <div className="flex items-center gap-1">
                          <Users className="w-4 h-4" />
                          <span>{age} years old</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <MapPin className="w-4 h-4" />
                          <span>{receiver.profile.town.name}</span>
                        </div>
                        {receiver.profile.tribe && (
                          <div className="flex items-center gap-1">
                            <span>{receiver.profile.tribe}</span>
                          </div>
                        )}
                      </div>

                      <div className="flex items-center gap-2">
                        <div className="flex items-center gap-1 text-xs text-gray-500">
                          <Clock className="w-3 h-3" />
                          <span>Sent {new Date(request.matchedAt).toLocaleDateString()}</span>
                        </div>
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                          Pending Response
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              )
            })
          )
        )}
      </div>
    </div>
  )
}
