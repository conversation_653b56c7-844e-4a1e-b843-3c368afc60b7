import { NextRequest, NextResponse } from 'next/server';
import { verifyJWT } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const token = authHeader.substring(7)
    const payload = verifyJWT(token)
    if (!payload?.userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user is admin
    const user = await prisma.user.findUnique({
      where: { id: payload.userId }
    })

    if (!user || user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    // Get query parameters
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const search = searchParams.get('search') || ''
    const status = searchParams.get('status') || 'all'

    const skip = (page - 1) * limit

    // Build where clause
    const where: any = {}

    // Search filter
    if (search) {
      where.OR = [
        { email: { contains: search, mode: 'insensitive' } },
        { name: { contains: search, mode: 'insensitive' } }
      ]
    }

    // Status filter
    if (status !== 'all') {
      switch (status) {
        case 'active':
          where.isActive = true
          break
        case 'inactive':
          where.isActive = false
          break
        case 'premium':
          where.subscriptions = {
            some: {
              status: 'ACTIVE'
            }
          }
          break
        case 'free':
          where.subscriptions = {
            none: {
              status: 'ACTIVE'
            }
          }
          break
      }
    }

    // Fetch users and total count
    const [users, totalCount] = await Promise.all([
      prisma.user.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
        select: {
          id: true,
          email: true,
          name: true,
          isActive: true,
          isPaid: true,
          subscriptionExpiresAt: true,
          role: true,
          createdAt: true,
          lastLoginAt: true,
          profile: {
            select: {
              fullName: true,
              town: {
                select: {
                  name: true
                }
              }
            }
          }
        }
      }),
      prisma.user.count({ where })
    ])

    // Format users data
    const formattedUsers = users.map(user => ({
      id: user.id,
      email: user.email,
      name: user.profile?.fullName || user.name || 'No name',
      location: user.profile?.town?.name,
      isActive: user.isActive,
      isPaid: user.isPaid,
      subscriptionExpiresAt: user.subscriptionExpiresAt?.toISOString(),
      role: user.role,
      subscriptionStatus: user.isPaid ? 'ACTIVE' : 'INACTIVE',
      createdAt: user.createdAt.toISOString(),
      lastActive: user.lastLoginAt?.toISOString()
    }))

    const totalPages = Math.ceil(totalCount / limit)

    return NextResponse.json({
      users: formattedUsers,
      totalCount,
      totalPages,
      currentPage: page
    })

  } catch (error) {
    console.error('Error fetching users:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 