import { NextRequest, NextResponse } from 'next/server'
import { verifyJWT } from '@/lib/auth'
import { verifyJWTEdge } from '@/lib/auth'

// Routes that require active subscription
const SUBSCRIPTION_REQUIRED_ROUTES = [
  '/api/matches',
  '/api/messages',
  '/api/matches/conversations'
]

// Routes that require profile completion
const PROFILE_REQUIRED_ROUTES = [
  '/api/matches',
  '/api/users/preferences'
]

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // Skip middleware for public routes
  if (pathname.startsWith('/_next') || 
      pathname.startsWith('/api/auth') ||
      pathname.startsWith('/api/towns') ||
      pathname.startsWith('/api/webhooks')) {
    return NextResponse.next()
  }

  // Check if route requires authentication
  if (pathname.startsWith('/api/') && !pathname.startsWith('/api/auth')) {
    const authHeader = request.headers.get('authorization')
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const token = authHeader.substring(7)
    const payload = await verifyJWTEdge(token)

    if (!payload?.userId) {
      return NextResponse.json(
        { error: 'Invalid token' },
        { status: 401 }
      )
    }

    // Check subscription requirements
    if (SUBSCRIPTION_REQUIRED_ROUTES.some(route => pathname.startsWith(route))) {
      // For now, we'll let the individual API routes handle subscription checks
      // This could be enhanced to check subscription status here
    }

    // Check profile completion requirements
    if (PROFILE_REQUIRED_ROUTES.some(route => pathname.startsWith(route))) {
      // For now, we'll let the individual API routes handle profile checks
      // This could be enhanced to check profile completion here
    }
  }

  // Handle client-side routes
  if (pathname.startsWith('/matches') ||
      pathname.startsWith('/messages') ||
      pathname.startsWith('/profile') ||
      pathname.startsWith('/settings')) {

    // Check for authentication token in cookies or headers
    const cookieToken = request.cookies.get('kenyamatch_token')?.value
    const headerToken = request.headers.get('authorization')?.replace('Bearer ', '')
    const token = cookieToken || headerToken

    if (!token) {
      return NextResponse.redirect(new URL('/login', request.url))
    }

    const payload = await verifyJWTEdge(token)

    if (!payload?.userId) {
      return NextResponse.redirect(new URL('/login', request.url))
    }
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    '/api/:path*',
    '/matches/:path*',
    '/messages/:path*',
    '/profile/:path*',
    '/settings/:path*'
  ]
}