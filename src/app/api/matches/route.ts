import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { verifyJWT } from '@/lib/auth'
import { calculateAge, calculateDistance } from '@/lib/utils'

export async function GET(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const token = authHeader.substring(7)
    const decoded = verifyJWT(token)
    if (!decoded) {
      return NextResponse.json(
        { success: false, error: 'Invalid token' },
        { status: 401 }
      )
    }

    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      include: {
        profile: {
          include: { town: true }
        },
        preferences: true,
        children: true
      }
    })

    if (!user || !user.profile) {
      return NextResponse.json(
        { success: false, error: 'Profile not found' },
        { status: 404 }
      )
    }

    // Get search parameters
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const skip = (page - 1) * limit

    // Get additional search parameters
    const genderFilter = searchParams.get('gender')
    const tribeFilter = searchParams.get('tribe')
    const religionFilter = searchParams.get('religion')
    const verifiedOnly = searchParams.get('verified') === 'true'
    const hasChildrenFilter = searchParams.get('hasChildren')
    const minAge = searchParams.get('minAge')
    const maxAge = searchParams.get('maxAge')
    const townFilter = searchParams.get('town')

    // Build search filters
    const filters: any = {
      AND: [
        { id: { not: user.id } },
        { isActive: true },
        { profile: { isNot: null } }
      ]
    }

    // Gender preference filter (use user's preference or override with search filter)
    const targetGender = genderFilter || user.profile.genderPreference
    if (targetGender) {
      filters.AND.push({
        profile: {
          gender: targetGender
        }
      })
    }

    // Additional search filters
    if (tribeFilter) {
      filters.AND.push({
        profile: {
          tribe: {
            contains: tribeFilter,
            mode: 'insensitive'
          }
        }
      })
    }

    if (religionFilter) {
      filters.AND.push({
        profile: {
          religion: {
            contains: religionFilter,
            mode: 'insensitive'
          }
        }
      })
    }

    if (verifiedOnly) {
      filters.AND.push({
        profile: {
          verified: true
        }
      })
    }

    if (townFilter) {
      filters.AND.push({
        profile: {
          town: {
            name: {
              contains: townFilter,
              mode: 'insensitive'
            }
          }
        }
      })
    }

    // Age preference filter (use search params or user preferences)
    const searchMinAge = minAge ? parseInt(minAge) : user.preferences?.preferredAgeMin
    const searchMaxAge = maxAge ? parseInt(maxAge) : user.preferences?.preferredAgeMax

    if (searchMinAge && searchMaxAge) {
      const currentYear = new Date().getFullYear()
      filters.AND.push({
        profile: {
          dateOfBirth: {
            gte: new Date(currentYear - searchMaxAge, 0, 1),
            lte: new Date(currentYear - searchMinAge, 11, 31)
          }
        }
      })
    }

    // Children preference filter (use search params or user preferences)
    const childrenPreference = hasChildrenFilter || user.preferences?.hasChildren
    if (childrenPreference && childrenPreference !== 'DOESNT_MATTER') {
      if (childrenPreference === 'YES') {
        filters.AND.push({ children: { isNot: null } })
      } else if (childrenPreference === 'NO') {
        filters.AND.push({ children: null })
      }
    }

    // Exclude already matched users
    const existingMatches = await prisma.match.findMany({
      where: {
        OR: [
          { user1Id: user.id },
          { user2Id: user.id }
        ]
      },
      select: {
        user1Id: true,
        user2Id: true
      }
    })

    const matchedUserIds = existingMatches.map(match => 
      match.user1Id === user.id ? match.user2Id : match.user1Id
    )

    if (matchedUserIds.length > 0) {
      filters.AND.push({
        id: { notIn: matchedUserIds }
      })
    }

    // Exclude blocked users
    const blockedUsers = await prisma.blockedUser.findMany({
      where: {
        OR: [
          { blockerId: user.id },
          { blockedId: user.id }
        ]
      },
      select: {
        blockerId: true,
        blockedId: true
      }
    })

    const blockedUserIds = blockedUsers.map(block => 
      block.blockerId === user.id ? block.blockedId : block.blockerId
    )

    if (blockedUserIds.length > 0) {
      filters.AND.push({
        id: { notIn: blockedUserIds }
      })
    }

    // Get potential matches
    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where: filters,
        include: {
          profile: {
            include: {
              town: true,
              profilePictures: {
                where: { isPrimary: true },
                take: 1
              }
            }
          },
          children: true
        },
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' }
      }),
      prisma.user.count({ where: filters })
    ])

    // Calculate distances and filter by max distance
    const filteredUsers = users.filter(potentialMatch => {
      if (!potentialMatch.profile || !user.profile) return false

      const distance = calculateDistance(
        user.profile.town.latitude,
        user.profile.town.longitude,
        potentialMatch.profile.town.latitude,
        potentialMatch.profile.town.longitude
      )

      return !user.preferences || distance <= user.preferences.maxDistance
    })

    return NextResponse.json({
      success: true,
      data: filteredUsers,
      pagination: {
        page,
        limit,
        total: filteredUsers.length,
        totalPages: Math.ceil(total / limit)
      }
    })

  } catch (error) {
    console.error('Get matches error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const token = authHeader.substring(7)
    const decoded = verifyJWT(token)
    if (!decoded) {
      return NextResponse.json(
        { success: false, error: 'Invalid token' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { targetUserId } = body

    if (!targetUserId) {
      return NextResponse.json(
        { success: false, error: 'Target user ID is required' },
        { status: 400 }
      )
    }

    // Check if user has active subscription
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId }
    })

    if (!user?.isPaid || !user.subscriptionExpiresAt || new Date() > user.subscriptionExpiresAt) {
      return NextResponse.json(
        {
          success: false,
          error: 'Active subscription required to match with users',
          requiresPayment: true,
          subscriptionStatus: {
            isPaid: user.isPaid,
            subscriptionExpiresAt: user.subscriptionExpiresAt
          }
        },
        { status: 403 }
      )
    }

    // Check if match already exists
    const existingMatch = await prisma.match.findFirst({
      where: {
        OR: [
          { user1Id: decoded.userId, user2Id: targetUserId },
          { user1Id: targetUserId, user2Id: decoded.userId }
        ]
      }
    })

    if (existingMatch) {
      return NextResponse.json(
        { success: false, error: 'Match already exists' },
        { status: 400 }
      )
    }

    // Create new match
    const match = await prisma.match.create({
      data: {
        user1Id: decoded.userId,
        user2Id: targetUserId,
        status: 'pending'
      },
      include: {
        user1: {
          include: {
            profile: {
              include: { town: true }
            }
          }
        },
        user2: {
          include: {
            profile: {
              include: { town: true }
            }
          }
        }
      }
    })

    // Create notification for the receiver
    await prisma.notification.create({
      data: {
        userId: targetUserId,
        type: 'match',
        title: 'New Match Request!',
        message: `${match.user1.profile?.fullName || 'Someone'} sent you a match request. Check it out!`,
        metadata: {
          matchId: match.id,
          senderId: decoded.userId,
          senderName: match.user1.profile?.fullName
        }
      }
    })

    return NextResponse.json({
      success: true,
      data: match,
      message: 'Match request sent successfully'
    })

  } catch (error) {
    console.error('Create match error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
} 