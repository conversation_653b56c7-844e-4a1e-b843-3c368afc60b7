// <PERSON><PERSON>t to set password for <PERSON> user
import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function setUserPassword() {
  try {
    // Use the user ID from the logs
    const userId = 'df64f500-ef4a-4f5c-bbea-6e80d3f64ef5'

    // Find user by ID
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        profile: true
      }
    })

    if (!user) {
      console.log(`User with ID ${userId} not found`)
      return
    }

    console.log('Found user:', {
      id: user.id,
      email: user.email,
      name: user.profile?.fullName || user.name
    })

    // Hash the password "jack75522r"
    const password = 'jack75522r'
    const hashedPassword = await bcrypt.hash(password, 12)

    // Update the user's password
    await prisma.user.update({
      where: { id: user.id },
      data: { passwordHash: hashedPassword }
    })

    console.log(`Password set successfully for ${user.email}`)
    console.log(`Email: ${user.email}`)
    console.log(`Password: ${password}`)

  } catch (error) {
    console.error('Error setting password:', error)
  } finally {
    await prisma.$disconnect()
  }
}

setUserPassword()
