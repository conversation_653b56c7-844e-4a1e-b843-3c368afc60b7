'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Phone, 
  CreditCard, 
  CheckCircle, 
  Clock, 
  AlertCircle,
  Copy,
  RefreshCw,
  ArrowLeft
} from 'lucide-react';

interface PaymentDetails {
  id: string;
  amount: number;
  currency: string;
  description: string;
  status: 'pending' | 'completed' | 'failed';
  mpesaCode?: string;
  phoneNumber?: string;
  createdAt: string;
}

export default function PaymentInstructionsPage() {
  const [payment, setPayment] = useState<PaymentDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [checkingStatus, setCheckingStatus] = useState(false);
  const [phoneNumber, setPhoneNumber] = useState('');
  const [mpesaCode, setMpesaCode] = useState('');
  const [step, setStep] = useState<'phone' | 'payment' | 'confirmation'>('phone');
  const router = useRouter();
  const searchParams = useSearchParams();
  const paymentId = searchParams.get('paymentId');

  useEffect(() => {
    if (paymentId) {
      fetchPaymentDetails();
    }
  }, [paymentId]);

  const fetchPaymentDetails = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/payments/${paymentId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setPayment(data.payment);
        
        if (data.payment.status === 'completed') {
          setStep('confirmation');
        } else if (data.payment.phoneNumber) {
          setPhoneNumber(data.payment.phoneNumber);
          setStep('payment');
        }
      }
    } catch (error) {
      console.error('Error fetching payment:', error);
    } finally {
      setLoading(false);
    }
  };

  const handlePhoneSubmit = async () => {
    if (!phoneNumber || !paymentId) return;

    setLoading(true);
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/payments/${paymentId}/initiate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ phoneNumber })
      });

      if (response.ok) {
        setStep('payment');
        fetchPaymentDetails(); // Refresh payment details
      }
    } catch (error) {
      console.error('Error initiating payment:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleMpesaCodeSubmit = async () => {
    if (!mpesaCode || !paymentId) return;

    setLoading(true);
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/payments/${paymentId}/confirm`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ mpesaCode })
      });

      if (response.ok) {
        setStep('confirmation');
        fetchPaymentDetails(); // Refresh payment details
      }
    } catch (error) {
      console.error('Error confirming payment:', error);
    } finally {
      setLoading(false);
    }
  };

  const checkPaymentStatus = async () => {
    setCheckingStatus(true);
    try {
      await fetchPaymentDetails();
    } finally {
      setCheckingStatus(false);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!payment) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6">
            <div className="text-center">
              <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
              <h2 className="text-xl font-semibold mb-2">Payment Not Found</h2>
              <p className="text-gray-600 mb-4">The payment you're looking for doesn't exist.</p>
              <Button onClick={() => router.push('/upgrade')}>
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Upgrade
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 py-12">
      <div className="container mx-auto px-4">
        <div className="max-w-2xl mx-auto">
          {/* Header */}
          <div className="text-center mb-8">
            <Button 
              variant="ghost" 
              onClick={() => router.push('/upgrade')}
              className="mb-4"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Upgrade
            </Button>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Complete Your Payment
            </h1>
            <p className="text-gray-600">
              Follow the steps below to complete your M-Pesa payment
            </p>
          </div>

          {/* Payment Summary */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle>Payment Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span>Amount:</span>
                  <span className="font-semibold">{payment.currency} {payment.amount.toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span>Description:</span>
                  <span className="text-gray-600">{payment.description}</span>
                </div>
                <div className="flex justify-between">
                  <span>Status:</span>
                  <Badge 
                    className={
                      payment.status === 'completed' ? 'bg-green-100 text-green-800' :
                      payment.status === 'failed' ? 'bg-red-100 text-red-800' :
                      'bg-yellow-100 text-yellow-800'
                    }
                  >
                    {payment.status.charAt(0).toUpperCase() + payment.status.slice(1)}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Step 1: Phone Number */}
          {step === 'phone' && (
            <Card className="mb-8">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Phone className="w-5 h-5" />
                  Step 1: Enter Your M-Pesa Phone Number
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      M-Pesa Phone Number
                    </label>
                    <Input
                      type="tel"
                      placeholder="e.g., ************"
                      value={phoneNumber}
                      onChange={(e) => setPhoneNumber(e.target.value)}
                      className="text-lg"
                    />
                    <p className="text-sm text-gray-500 mt-1">
                      Enter the phone number registered with your M-Pesa account
                    </p>
                  </div>
                  <Button 
                    onClick={handlePhoneSubmit}
                    disabled={!phoneNumber || loading}
                    className="w-full"
                  >
                    {loading ? 'Processing...' : 'Continue to Payment'}
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Step 2: Payment Instructions */}
          {step === 'payment' && (
            <Card className="mb-8">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CreditCard className="w-5 h-5" />
                  Step 2: Complete M-Pesa Payment
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <Alert>
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      You will receive an M-Pesa prompt on your phone. Please complete the payment.
                    </AlertDescription>
                  </Alert>

                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h4 className="font-semibold mb-3">Payment Instructions:</h4>
                    <ol className="list-decimal list-inside space-y-2 text-sm">
                      <li>You will receive an M-Pesa prompt on your phone</li>
                      <li>Enter your M-Pesa PIN when prompted</li>
                      <li>Confirm the payment amount and recipient</li>
                      <li>Wait for the confirmation message</li>
                      <li>Enter the M-Pesa transaction code below</li>
                    </ol>
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">
                      M-Pesa Transaction Code
                    </label>
                    <div className="flex gap-2">
                      <Input
                        placeholder="e.g., QK123456789"
                        value={mpesaCode}
                        onChange={(e) => setMpesaCode(e.target.value.toUpperCase())}
                        className="text-lg font-mono"
                      />
                      <Button
                        variant="outline"
                        onClick={() => copyToClipboard(mpesaCode)}
                        disabled={!mpesaCode}
                      >
                        <Copy className="w-4 h-4" />
                      </Button>
                    </div>
                    <p className="text-sm text-gray-500 mt-1">
                      Enter the transaction code from your M-Pesa confirmation message
                    </p>
                  </div>

                  <div className="flex gap-3">
                    <Button 
                      onClick={handleMpesaCodeSubmit}
                      disabled={!mpesaCode || loading}
                      className="flex-1"
                    >
                      {loading ? 'Verifying...' : 'Confirm Payment'}
                    </Button>
                    <Button
                      variant="outline"
                      onClick={checkPaymentStatus}
                      disabled={checkingStatus}
                    >
                      <RefreshCw className={`w-4 h-4 ${checkingStatus ? 'animate-spin' : ''}`} />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Step 3: Confirmation */}
          {step === 'confirmation' && (
            <Card className="mb-8">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CheckCircle className="w-5 h-5 text-green-500" />
                  Payment Successful!
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center space-y-6">
                  <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                    <CheckCircle className="w-8 h-8 text-green-500" />
                  </div>
                  
                  <div>
                    <h3 className="text-xl font-semibold mb-2">Payment Confirmed</h3>
                    <p className="text-gray-600">
                      Your payment has been successfully processed. Your premium features are now active!
                    </p>
                  </div>

                  <div className="bg-green-50 p-4 rounded-lg">
                    <div className="text-sm space-y-1">
                      <div className="flex justify-between">
                        <span>Transaction ID:</span>
                        <span className="font-mono">{payment.id}</span>
                      </div>
                      {payment.mpesaCode && (
                        <div className="flex justify-between">
                          <span>M-Pesa Code:</span>
                          <span className="font-mono">{payment.mpesaCode}</span>
                        </div>
                      )}
                      <div className="flex justify-between">
                        <span>Amount:</span>
                        <span className="font-semibold">{payment.currency} {payment.amount.toLocaleString()}</span>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <Button
                      onClick={() => router.push('/matches')}
                      className="w-full"
                    >
                      Go to Matches
                    </Button>
                    <Button 
                      variant="outline"
                      onClick={() => router.push('/profile')}
                      className="w-full"
                    >
                      Update Profile
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Help Section */}
          <Card>
            <CardHeader>
              <CardTitle>Need Help?</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 text-sm">
                <p className="text-gray-600">
                  If you encounter any issues with your payment, please contact our support team:
                </p>
                <div className="space-y-1">
                  <div>📧 Email: <EMAIL></div>
                  <div>📱 WhatsApp: +254 700 000 000</div>
                  <div>📞 Phone: +254 700 000 000</div>
                </div>
                <p className="text-gray-500 text-xs">
                  Support hours: Monday - Friday, 8:00 AM - 6:00 PM EAT
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
} 