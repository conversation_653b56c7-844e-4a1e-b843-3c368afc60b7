import { useEffect, useState } from 'react'

interface NotificationCounts {
  totalUnread: number
  matchRequests: number
  messages: number
}

export function useNotificationCount() {
  const [counts, setCounts] = useState<NotificationCounts>({
    totalUnread: 0,
    matchRequests: 0,
    messages: 0
  })
  const [loading, setLoading] = useState(true)

  const fetchCounts = async () => {
    try {
      const token = localStorage.getItem('kenyamatch_token')
      if (!token) return

      // Fetch match requests count
      const matchRequestsRes = await fetch('/api/matches/requests', {
        headers: { Authorization: `Bearer ${token}` }
      })
      
      let matchRequestsCount = 0
      if (matchRequestsRes.ok) {
        const matchRequestsData = await matchRequestsRes.json()
        if (matchRequestsData.success) {
          matchRequestsCount = matchRequestsData.data.incoming.length
        }
      }

      // Fetch notifications count
      const notificationsRes = await fetch('/api/notifications?limit=100', {
        headers: { Authorization: `Bearer ${token}` }
      })
      
      let totalUnread = 0
      let messagesCount = 0
      if (notificationsRes.ok) {
        const notificationsData = await notificationsRes.json()
        if (notificationsData.notifications) {
          const unreadNotifications = notificationsData.notifications.filter((n: any) => !n.isRead)
          totalUnread = unreadNotifications.length
          messagesCount = unreadNotifications.filter((n: any) => n.type === 'message').length
        }
      }

      setCounts({
        totalUnread,
        matchRequests: matchRequestsCount,
        messages: messagesCount
      })
    } catch (error) {
      console.error('Failed to fetch notification counts:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchCounts()
    
    // Refresh counts every 30 seconds
    const interval = setInterval(fetchCounts, 30000)
    
    return () => clearInterval(interval)
  }, [])

  return { counts, loading, refetch: fetchCounts }
}
